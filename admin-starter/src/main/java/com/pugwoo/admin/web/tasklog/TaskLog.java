package com.pugwoo.admin.web.tasklog;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 作为TaskLog保存数据的标志注解
 * 加了这个注解的定时任务方法，会自动将定时任务方法的执行情况保存到t_admin_task和t_admin_task_log表中
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TaskLog {

    /**
     * 定时任务名称，必须
     */
    String taskName();

    /**
     * 任务编号，任务的唯一标识，可选，默认使用className.methodName作为唯一标识
     */
    String taskCode() default "";

    /**
     * 超时时长(秒)，默认值为-1，表示不设置超时；若使用者已知任务执行的耗时上限时，最好指定这个属性以更好监控任务执行
     */
    int timeout() default -1;

}
