package com.pugwoo.admin.web.tasklog;

import com.pugwoo.admin.entity.AdminTaskDO;
import com.pugwoo.admin.entity.AdminTaskLogDO;
import com.pugwoo.admin.enums.AdminTaskStatusEnum;
import com.pugwoo.admin.utils.ClassUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Aspect
@Component
@Slf4j
public class TaskLogAOP {

    /**  采用线程池，主线程执行业务代码，遇到监控的逻辑新开线程去执行，保证事务不共用 */
    private final ExecutorService pool = Executors.newFixedThreadPool(1);

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @Around("@annotation(com.pugwoo.admin.web.tasklog.TaskLog) || @annotation(org.springframework.scheduling.annotation.Scheduled)")
    public Object aroundMethod(ProceedingJoinPoint joinpoint) throws Throwable {
        //  1. 获取注解内的TaskName 获取切点的方法签名
        MethodSignature signature = (MethodSignature) joinpoint.getSignature();
        //  获取方法名称
        Method method = signature.getMethod();
        Object[] args = joinpoint.getArgs();

        TaskLog taskLog = method.getAnnotation(TaskLog.class);
        Scheduled scheduled = method.getAnnotation(Scheduled.class);

        long start = System.currentTimeMillis();

        //  2. 提前检查任务是否被禁用
        String taskCode = getTaskCode(method, taskLog);
        AdminTaskDO existingTask = dbHelper.getOne(AdminTaskDO.class, "where task_code=?", taskCode);

        if (existingTask != null && Boolean.TRUE.equals(existingTask.getCtrlDisabled())) {
            // 任务被禁用，直接记录跳过状态并返回
            pool.submit(() -> recordSkippedTask(method, args, taskLog, scheduled, existingTask));
            return null; // 不执行实际任务，直接返回
        }

        //  3. 前置处理：插入TaskLogDO对象
        Future<AdminTaskLogDO> taskLogDO = pool.submit(() -> generateNewOne(method, args, taskLog, scheduled, AdminTaskStatusEnum.NEW));

        //  4. 调用接口方法(如果有异常，在下面的try-catch块中捕获、记录后再抛出)
        Object result;
        try {
            //  真正调用接口方法
            result = joinpoint.proceed();
            //  5. 成功调用时记录执行时长，回写DO
            pool.submit(() -> updateSuccessfulOne(taskLogDO, start));
            //  6. 返回调用结果，监控只做记录
            return result;
        } catch (Throwable e) {
            //  如果catch到了异常，执行失败并获取对应的报错栈信息，回写Log对象
            StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer, true));
            //  更新异常记录对象
            pool.submit(() -> updateFailedOne(taskLogDO, writer.toString(), start));
            //  继续抛出异常，监控只做记录
            throw e;
        }
    }

    private AdminTaskLogDO generateNewOne(Method method, Object[] args,
                                          TaskLog taskLog, Scheduled scheduled, AdminTaskStatusEnum status){
        // 1. 创建AdminTaskLogDO
        String taskCode = getTaskCode(method, taskLog);
        AdminTaskLogDO adminTaskLogDO = buildAdminTaskLogDO(method, args, taskLog, scheduled, taskCode);
        adminTaskLogDO.setStatus(status.getCode());

        // 2. 处理AdminTaskDO的插入或更新
        AdminTaskDO adminTaskDO = createOrUpdateAdminTask(adminTaskLogDO);
        adminTaskLogDO.setTaskId(adminTaskDO.getId()); // 关联任务ID

        dbHelper.insert(adminTaskLogDO);
        return adminTaskLogDO;
    }

    /**
     * 创建或更新AdminTaskDO记录
     * 以taskCode为唯一标识，当AdminTaskDO不存在该taskCode则插入，有且字段有变化则更新
     */
    private AdminTaskDO createOrUpdateAdminTask(AdminTaskLogDO adminTaskLogDO) {
        // 1. 根据taskCode查询现有记录
        AdminTaskDO existingTask = dbHelper.getOne(AdminTaskDO.class, "where task_code=?", adminTaskLogDO.getTaskCode());

        // 2. 构建新的任务信息
        if (existingTask == null) {
            AdminTaskDO newTaskInfo = new AdminTaskDO();
            newTaskInfo.setTaskCode(adminTaskLogDO.getTaskCode());
            newTaskInfo.setTaskName(adminTaskLogDO.getTaskName());
            newTaskInfo.setClassName(adminTaskLogDO.getClassName());
            newTaskInfo.setMethodName(adminTaskLogDO.getMethodName());
            newTaskInfo.setCronExpression(adminTaskLogDO.getCronExpression());
            newTaskInfo.setFixRateMs(adminTaskLogDO.getFixRateMs());
            newTaskInfo.setTimeoutSecond(adminTaskLogDO.getTimeoutSecond());
            newTaskInfo.setIsLastSuccess(true); // 新任务默认为成功
            newTaskInfo.setLastTime(LocalDateTime.now());
            newTaskInfo.setLastErrorTime(null);
            newTaskInfo.setCountSuccess(0);
            newTaskInfo.setCountError(0);
            dbHelper.insert(newTaskInfo);
            return newTaskInfo;
        }

        return existingTask;
    }

    /**
     * 获取任务代码
     */
    private String getTaskCode(Method method, TaskLog taskLog) {
        if (taskLog != null && StringTools.isNotBlank(taskLog.taskCode())) {
            return taskLog.taskCode();
        } else {
            return method.getDeclaringClass().getName() + "." + ClassUtils.getMethodSignature(method);
        }
    }

    /**
     * 构建AdminTaskLogDO的公共方法
     */
    private AdminTaskLogDO buildAdminTaskLogDO(Method method, Object[] args, TaskLog taskLog, 
                                               Scheduled scheduled, String taskCode) {
        AdminTaskLogDO adminTaskLogDO = new AdminTaskLogDO();
        adminTaskLogDO.setTaskName(taskLog == null ? "" : taskLog.taskName());
        adminTaskLogDO.setClassName(method.getDeclaringClass().getName());
        adminTaskLogDO.setMethodName(ClassUtils.getMethodSignature(method));
        adminTaskLogDO.setTaskCode(taskCode);

        if (scheduled != null) {
            adminTaskLogDO.setCronExpression(scheduled.cron());
            if (scheduled.fixedRate() != 0) {
                adminTaskLogDO.setFixRateMs((int) scheduled.fixedRate());
            } else if (scheduled.fixedDelay() != 0) {
                adminTaskLogDO.setFixRateMs((int) scheduled.fixedDelay());
            }
        }

        try {
            adminTaskLogDO.setRunIp(String.join(";", NetUtils.getIpv4IPs()));
        } catch (Exception e) {
            adminTaskLogDO.setRunIp("EXCEPTION " + e.getClass().getName());
            log.error("fail to get machine ips", e);
        }

        Integer timeout = taskLog == null ? -1 : taskLog.timeout();
        adminTaskLogDO.setTimeoutSecond(Objects.equals(timeout, -1) ? null : timeout);
        adminTaskLogDO.setArgs(JSON.toJson(args));
        adminTaskLogDO.setCostMs(0);

        return adminTaskLogDO;
    }

    private void updateTaskInfo(AdminTaskDO existingTask, AdminTaskLogDO adminTaskLogDO) {
        existingTask.setTaskName(adminTaskLogDO.getTaskName());
        existingTask.setClassName(adminTaskLogDO.getClassName());
        existingTask.setMethodName(adminTaskLogDO.getMethodName());
        existingTask.setCronExpression(adminTaskLogDO.getCronExpression());
        existingTask.setFixRateMs(adminTaskLogDO.getFixRateMs());
        existingTask.setTimeoutSecond(adminTaskLogDO.getTimeoutSecond());
    }


    private void updateFailedOne(Future<AdminTaskLogDO> future, String errorMsg, long start) {
        long end = System.currentTimeMillis();
        AdminTaskLogDO one = null;
        try {
            one = future.get();
            one.setCostMs((int)(end - start));
            one.setStatus(AdminTaskStatusEnum.FAIL.getCode());
            one.setErrorMsg(errorMsg);
            dbHelper.update(one);
            
            // 更新任务统计信息
            if (one.getTaskId() != null) {
                AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, one.getTaskId());
                if (task != null) {
                    updateTaskInfo(task, one);
                    task.setIsLastSuccess(false);
                    task.setLastErrorTime(LocalDateTime.now());
                    task.setCountError(task.getCountError() == null ? 1 : task.getCountError() + 1);
                    dbHelper.update(task);
                }
            }
        } catch (Exception e) {
            log.error("【定时任务执行失败】更新失败:{}", JSON.toJson(one), e);
        }
    }

    private void updateSuccessfulOne(Future<AdminTaskLogDO> future, long start) {
        long end = System.currentTimeMillis();
        AdminTaskLogDO one = null;
        try {
            one = future.get();
            one.setCostMs((int)(end - start));
            one.setStatus(AdminTaskStatusEnum.SUCCESS.getCode());
            dbHelper.update(one);
            
            // 更新任务统计信息
            if (one.getTaskId() != null) {
                AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, one.getTaskId());
                if (task != null) {
                    updateTaskInfo(task, one);
                    task.setIsLastSuccess(true);
                    task.setLastTime(LocalDateTime.now());
                    task.setCountSuccess(task.getCountSuccess() == null ? 1 : task.getCountSuccess() + 1);
                    dbHelper.update(task);
                }
            }
        } catch (Exception e) {
            log.error("【定时任务执行成功】更新失败:{}", JSON.toJson(one), e);
        }
    }

    /**
     * 记录被跳过的任务
     */
    private void recordSkippedTask(Method method, Object[] args, TaskLog taskLog,
                                   Scheduled scheduled, AdminTaskDO existingTask) {
        try {
            // 创建AdminTaskLogDO
            AdminTaskLogDO adminTaskLogDO = buildAdminTaskLogDO(method, args, taskLog, scheduled, existingTask.getTaskCode());
            adminTaskLogDO.setStatus(AdminTaskStatusEnum.SKIPPED.getCode());
            adminTaskLogDO.setErrorMsg("任务已被禁用，不需要执行");
            adminTaskLogDO.setTaskId(existingTask.getId());

            dbHelper.insert(adminTaskLogDO);

            updateTaskInfo(existingTask, adminTaskLogDO);
            dbHelper.update(existingTask);

        } catch (Exception e) {
            log.error("【定时任务跳过执行】记录失败", e);
        }
    }

}
