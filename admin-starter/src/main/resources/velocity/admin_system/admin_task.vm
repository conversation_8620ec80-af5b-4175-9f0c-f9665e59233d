<!-- admin_task.vm -->

#set($page_title='任务列表')

#parse("admin_system/page_components/admin_task_log.vm")

<style>
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
        <el-form-item label="id">
            <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
        </el-form-item>
        <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
        <el-form-item>
            <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
            <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column prop="id" label="ID" width="60px"></el-table-column>
##        <el-table-column prop="createTime" label="创建时间"></el-table-column>
##        <el-table-column prop="updateTime" label="更新时间"></el-table-column>
        <el-table-column prop="taskName" label="任务名称" width="200px"></el-table-column>
        <el-table-column prop="taskCode" label="任务编号"></el-table-column>
        <el-table-column prop="ctrlDisabled" label="是否禁用" width="80px">
            <template slot-scope="scope">
                <span v-if="scope.row.ctrlDisabled" style="color: red">禁用</span>
                <span v-else style="color: green">启用</span>
            </template>
        </el-table-column>
        <el-table-column prop="className" label="类名称"></el-table-column>
        <el-table-column prop="methodName" label="方法名称" width="200px"></el-table-column>
        <el-table-column prop="cronExpression" label="Cron表达式" width="150px"></el-table-column>
        <el-table-column prop="fixRateMs" label="固定频率(ms)" width="80px"></el-table-column>
        <el-table-column prop="timeoutSecond" label="超时时间(秒)" width="100px"></el-table-column>
        <el-table-column prop="isLastSuccess" label="最后成功" width="80px">
            <template slot-scope="scope">
                <span v-if="scope.row.isLastSuccess" style="color: green">是</span>
                <span v-else style="color: red">否</span>
            </template>
        </el-table-column>
        <el-table-column prop="lastTime" label="最后运行时间" width="160px"></el-table-column>
        <el-table-column prop="lastErrorTime" label="最后失败时间" width="160px"></el-table-column>
        <el-table-column prop="countSuccess" label="成功次数" width="80px"></el-table-column>
        <el-table-column prop="countError" label="失败次数" width="80px"></el-table-column>
        <el-table-column label="操作" width="150px">
            <template slot-scope="scope">
                <el-button-group>
                    <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                    <el-button type="success" size="small" @click="showTaskLog(scope.row)">结果</el-button>
                </el-button-group>
            </template>
        </el-table-column>
    </el-table>

    <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>

    <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false">
        <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
            <el-form-item label="任务名称" prop="taskName">
                <el-input v-model="addEditForm.taskName" placeholder="任务名称"></el-input>
            </el-form-item>
            <el-form-item label="任务编号" prop="taskCode">
                <el-input v-model="addEditForm.taskCode" placeholder="任务编号，任务的唯一标识，可以指定，没有指定时值为class_name.method_name"></el-input>
            </el-form-item>
            <el-form-item label="是否禁用" prop="ctrlDisabled">
                <el-switch v-model="addEditForm.ctrlDisabled" active-text="禁用" inactive-text="启用"></el-switch>
            </el-form-item>
            <el-form-item label="类名称" prop="className">
                <el-input v-model="addEditForm.className" placeholder="执行的类的名称"></el-input>
            </el-form-item>
            <el-form-item label="方法名称" prop="methodName">
                <el-input v-model="addEditForm.methodName" placeholder="执行的方法名称"></el-input>
            </el-form-item>
            <el-form-item label="Cron表达式" prop="cronExpression">
                <el-input v-model="addEditForm.cronExpression" placeholder="如果是定时任务有cron表达式，则记录这里"></el-input>
            </el-form-item>
            <el-form-item label="固定频率(ms)" prop="fixRateMs">
                <el-input v-model="addEditForm.fixRateMs" placeholder="如果任务有固定的频率，fixed和delay都记录在这里"></el-input>
            </el-form-item>
            <el-form-item label="超时时间(秒)" prop="timeoutSecond">
                <el-input v-model="addEditForm.timeoutSecond" placeholder="任务执行超时时间（秒）"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
            <el-button @click="showDialog = false">取消</el-button>
            <el-button type="primary" @click="doAddOrEdit">确定</el-button>
        </div>
    </el-dialog>

    <!-- 任务日志弹框 -->
    <el-dialog title="任务运行日志" :visible.sync="showTaskLogDialog" width="98%" top="10px" :close-on-click-modal="false" v-if="showTaskLogDialog"> <!-- v-if让窗口关闭时自动销毁 -->
        <admin-task-log :task-id="selectedTaskId" v-if="showTaskLogDialog"></admin-task-log>
        <div slot="footer">
            <el-button @click="showTaskLogDialog = false">关闭</el-button>
        </div>
    </el-dialog>

</div>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    var vm = new Vue({
        el: '#app',
        data: {
            queryForm: Utils.copy(defaultQueryForm),
            addEditForm: Utils.copy(defaultAddForm),
            rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
            total: 0, tableData: [], tableLoading: false,
            showDialog: false, dialogTitle: '',
            showTaskLogDialog: false, selectedTaskId: null
        },
        created: function() {
            this.getData()
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                Resource.get("${_contextPath_}/admin_task/get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/admin_task/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增任务表' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/admin_task/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            },
            showTaskLog: function(row) {
                this.selectedTaskId = row.id
                this.showTaskLogDialog = true
            }
        }
    })
</script>